<template>
  <view class="container">
    <!-- 添加状态栏占位元素 -->
    <view class="status-bar-placeholder"></view>
    <view class="title">选择训练模式</view>
    <view class="training-list">
      <view class="training-item" @click="goToStoryRetelling">
        <view class="item-icon-wrapper">
          <view class="item-icon">
            <i class="fas fa-book-open"></i>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">故事复述</view>
          <view class="item-description">根据故事复述训练，提升表达能力</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <view class="training-item" @click="goToKeywordStory">
        <view class="item-icon-wrapper">
          <view class="item-icon">
            <i class="fas fa-lightbulb"></i>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">关键词讲故事</view>
          <view class="item-description">根据关键词创造并讲述故事，锻炼对概念的熟练使用</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <view class="training-item" @click="goToPutonghuaTraining">
        <view class="item-icon-wrapper">
          <view class="item-icon">
            <i class="fas fa-bullhorn"></i>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">普通话训练</view>
          <view class="item-description">单字练习，提升发音准确性</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <view class="training-item" @click="goToAiAssistant">
        <view class="item-icon-wrapper">
          <view class="item-icon">
            <i class="fas fa-robot"></i>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">AI 助教</view>
          <view class="item-description">与 AI 进行对话练习</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <view class="training-item" @click="copyClientId">
        <view class="item-icon-wrapper">
          <view class="item-icon">
            <i class="fas fa-copy"></i>
          </view>
        </view>
        <view class="item-content">
          <view class="item-title">复制客户端标识</view>
          <view class="item-description">获取并复制设备的推送客户端标识</view>
        </view>
        <view class="item-arrow">
          <i class="fas fa-chevron-right"></i>
        </view>
      </view>
      <!-- 更多训练模式可以加在这里 -->
    </view>

    <!-- 临时测试按钮 -->
    <view class="temp-test-section">
      <view class="title">临时测试按钮</view>
      <view class="training-item" @click="testGetTasks">
        <view class="item-content">
          <view class="item-title">获取任务列表</view>
        </view>
      </view>
      <view class="training-item" @click="testCreateTask">
        <view class="item-content">
          <view class="item-title">创建任务</view>
        </view>
      </view>
      <view class="training-item" @click="testGetProjects">
        <view class="item-content">
          <view class="item-title">获取项目列表</view>
        </view>
      </view>
      <view class="training-item" @click="testGetTask">
        <view class="item-content">
          <view class="item-title">获取单条任务</view>
        </view>
      </view>
    </view>
    <!-- 临时测试按钮结束 -->
  </view>
</template>

<script setup>
import { router } from '@/utils/tools'

const didaApi = uniCloud.importObject('dida-todo', {
  customUI: true, // uniCloud 会自动弹出 loading
})

const testGetTasks = async () => {
  try {
    uni.showLoading({ title: '加载中...' })
    const res = await didaApi.getTasks({
      completed: false,
      limit: 10,
    })
    uni.hideLoading()
    console.log('获取任务列表成功：', res)
    uni.showToast({ title: '获取成功，请看控制台', icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    console.error('获取任务列表失败：', err)
    uni.showToast({ title: '获取失败，请看控制台', icon: 'none' })
  }
}

const testCreateTask = async () => {
  try {
    uni.showLoading({ title: '创建中...' })
    const res = await didaApi.createTask({
      taskData: {
        title: `测试任务 - ${new Date().toLocaleString()}`,
        content: '这是一个通过 API 创建的测试任务',
      },
    })
    uni.hideLoading()
    console.log('创建任务成功：', res)
    uni.showToast({ title: '创建成功，请看控制台', icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    console.error('创建任务失败：', err)
    uni.showToast({ title: '创建失败，请看控制台', icon: 'none' })
  }
}

const testGetProjects = async () => {
  try {
    uni.showLoading({ title: '加载中...' })
    const res = await didaApi.getProjects()
    uni.hideLoading()
    console.log('获取项目列表成功：', res)
    uni.showToast({ title: '获取成功，请看控制台', icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    console.error('获取项目列表失败：', err)
    uni.showToast({ title: '获取失败，请看控制台', icon: 'none' })
  }
}

const testGetTask = async () => {
  try {
    uni.showLoading({ title: '加载中...' })
    // 使用测试参数调用获取单条任务接口
    // 注意：这里使用的是示例参数，实际使用时需要替换为真实的 projectId 和 taskId
    const res = await didaApi.getTask({
      projectId: '5a9649f19fcffccade184229',
      taskId: '543246ffa5d7d971f6738b12',
    })
    uni.hideLoading()
    console.log('获取单条任务成功：', res)
    uni.showToast({ title: '获取成功，请看控制台', icon: 'none' })
  } catch (err) {
    uni.hideLoading()
    console.error('获取单条任务失败：', err)
    uni.showToast({ title: '获取失败，请看控制台', icon: 'none' })
  }
}

const goToStoryRetelling = () => {
  router.push('/pages/speak/vox-flow?type=retell-story')
}

const goToKeywordStory = () => {
  router.push('/pages/speak/keyword-story-page')
}

const goToPutonghuaTraining = () => {
  router.push('/pages/speak/putonghua-training')
}

const goToAiAssistant = () => {
  router.push('/pages/aiAssistant/index')
}

const copyClientId = () => {
  uni.getPushClientId({
    success: (res) => {
      let push_clientid = res.cid
      console.log('客户端推送标识：', push_clientid)

      // 复制到剪贴板
      uni.setClipboardData({
        data: push_clientid,
        success: () => {
          uni.showToast({
            title: '客户端标识已复制',
            icon: 'success',
            duration: 2000,
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 2000,
          })
        },
      })
    },
    fail(err) {
      console.log('获取客户端标识失败：', err)
      uni.showToast({
        title: '获取客户端标识失败',
        icon: 'none',
        duration: 2000,
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f8f9fa;
  /* 使用更柔和的背景色 */
  min-height: 100vh;
}

// 添加状态栏占位样式
.status-bar-placeholder {
  height: var(--status-bar-height, 44px);
  width: 100%;
}

.title {
  font-size: 42rpx;
  /* 增大标题字号 */
  font-weight: bold;
  margin-bottom: 40rpx;
  /* 增加与列表的间距 */
  color: #2c3e50;
  /* 使用更深邃的颜色 */
  text-align: center;
  /* 标题居中 */
}

.training-list {
  display: flex;
  flex-direction: column;
}

.training-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  /* 增加更自然的阴影 */
}

.training-item:last-child {
  margin-bottom: 0;
}

.training-item:hover {
  transform: translateY(-5rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);
}

.training-item:active {
  background-color: #f7f7f7;
}

.item-icon-wrapper {
  margin-right: 30rpx;
}

.item-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eef2f7;
  /* 图标背景色 */
}

.item-icon i {
  font-size: 40rpx;
  color: #4a90e2;
  /* 图标颜色 */
}

/* 为不同项目设置不同颜色 */
.training-item:nth-child(1) .item-icon {
  background-color: #e8f5e9;
}

.training-item:nth-child(1) .item-icon i {
  color: #4caf50;
}

.training-item:nth-child(2) .item-icon {
  background-color: #fff3e0;
}

.training-item:nth-child(2) .item-icon i {
  color: #ff9800;
}

.training-item:nth-child(3) .item-icon {
  background-color: #e3f2fd;
}

.training-item:nth-child(3) .item-icon i {
  color: #2196f3;
}

.training-item:nth-child(4) .item-icon {
  background-color: #f0eaff;
}

.training-item:nth-child(4) .item-icon i {
  color: #673ab7;
}

.training-item:nth-child(5) .item-icon {
  background-color: #fce4ec;
}

.training-item:nth-child(5) .item-icon i {
  color: #e91e63;
}
.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.item-description {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4; /* 增加行高 */
}

.item-arrow i {
  font-size: 28rpx;
  color: #bdc3c7;
}

.temp-test-section {
  margin-top: 50rpx;
  padding: 20rpx;
  border: 2rpx dashed #ccc;
  border-radius: 20rpx;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #999;
    margin-bottom: 20rpx;
  }

  .training-item {
    background-color: #f0f0f0;
  }
}
</style>
