# 滴答清单 API 文档

## 基础信息

- 基础 URL: `https://api.dida365.com`
- 认证方式：Bearer Token
- 请求头：`Authorization: Bearer {{token}}`

## API 接口

### 1. 获取任务详情

**接口路径**: `GET /open/v1/project/{projectId}/task/{taskId}`

**描述**: 根据项目 ID 和任务 ID 获取任务详情

**路径参数**:

- `projectId` (必需): 项目标识符 (string)
- `taskId` (必需): 任务标识符 (string)

**响应状态码**:

- `200`: 成功返回任务信息
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到

**请求示例**:

```http
GET /open/v1/project/{{projectId}}/task/{{taskId}} HTTP/1.1
Host: api.dida365.com
Authorization: Bearer {{token}}
```

**响应示例**:

```json
{
  "id": "63b7bebb91c0a5474805fcd4",
  "isAllDay": true,
  "projectId": "6226ff9877acee87727f6bca",
  "title": "Task Title",
  "content": "Task Content",
  "desc": "Task Description",
  "timeZone": "America/Los_Angeles",
  "repeatFlag": "RRULE:FREQ=DAILY;INTERVAL=1",
  "startDate": "2019-11-13T03:00:00+0000",
  "dueDate": "2019-11-14T03:00:00+0000",
  "reminders": ["TRIGGER:P0DT9H0M0S", "TRIGGER:PT0S"],
  "priority": 1,
  "status": 0,
  "completedTime": "2019-11-13T03:00:00+0000",
  "sortOrder": 12345,
  "items": [
    {
      "id": "6435074647fd2e6387145f20",
      "status": 0,
      "title": "Item Title",
      "sortOrder": 12345,
      "startDate": "2019-11-13T03:00:00+0000",
      "isAllDay": false,
      "timeZone": "America/Los_Angeles",
      "completedTime": "2019-11-13T03:00:00+0000"
    }
  ]
}
```

**字段说明**:

- `id`: 任务唯一标识符
- `isAllDay`: 是否为全天任务
- `projectId`: 所属项目 ID
- `title`: 任务标题
- `content`: 任务内容
- `desc`: 任务描述
- `timeZone`: 时区
- `repeatFlag`: 重复规则 (RRULE 格式)
- `startDate`: 开始时间
- `dueDate`: 截止时间
- `reminders`: 提醒设置数组
- `priority`: 优先级 (数字)
- `status`: 任务状态 (0: 未完成)
- `completedTime`: 完成时间
- `sortOrder`: 排序顺序
- `items`: 子任务列表



